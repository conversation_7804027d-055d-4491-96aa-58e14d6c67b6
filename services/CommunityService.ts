import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class CommunityService {
  static async createCommunity(communityDetails: {
    userId: string;
    communityName: string;
    communityDescription: string;
    communityImages?: File[];
  }) {
    try {
      const formData = new FormData();
      formData.append('userId', communityDetails.userId);
      formData.append('communityName', communityDetails.communityName);
      formData.append('communityDescription', communityDetails.communityDescription);

      if (communityDetails.communityImages && communityDetails.communityImages.length > 0) {
        communityDetails.communityImages.forEach((image, index) => {
          formData.append('communityImages', {
            uri: image,
            name: `community_image_${index}.jpg`,
            type: 'image/jpeg',
          } as any);
        });
      }

      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/community/v1/create-community', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async joinCommunity(communityId: string, userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post(
        '/community/v1/join-community',
        {
          communityId,
          userId,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async leaveCommunity(communityId: string, userId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.delete(
        `/community/v1/leave-community?userId=${userId}&communityId=${communityId}`,

        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getCommunities(userId: string) {
    console.log('get communities1');
    console.log(userId);
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get(
        `/community/v1/get-communities?userId=${userId}&all=true&uninvolved=true&involved=false`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log('communities response', response.data);

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getUserCommunities(userId: string) {
    console.log('get communities2');
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get(
        `/community/v1/get-communities?all=true&uninvolved=false&userId=${userId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log('communities response1', response.data);
      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async removeUserFromCommunity(userId: string, communityId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.delete(
        `/community/v1/remove-user-from-community?userId=${userId}&communityId=${communityId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
