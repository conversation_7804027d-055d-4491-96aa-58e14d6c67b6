import * as SecureStore from 'expo-secure-store';

import axiosInstance from './config';
import { TicketApiResponse, TicketResponse, Ticket } from '../types/ticket';

class TicketService {
  /**
   * Fetch tickets for a specific user
   */
  async getTicketsByUser(userId: string): Promise<TicketResponse[]> {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get<TicketApiResponse>(
        `/ticket/v1/get-tickets-by-user?userId=${userId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        // Filter only PAID tickets
        return response.data.body.filter((ticket) => ticket.ticketStatus === 'PAID');
      }
      throw new Error(response.data.message || 'Failed to fetch tickets');
    } catch (error) {
      console.error('Error fetching tickets:', error);
      throw error;
    }
  }

  /**
   * Fetch tickets for a specific event
   */
  async getTicketsByEvent(eventId: string): Promise<TicketResponse[]> {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get<TicketApiResponse>(
        `/ticket/v1/get-tickets-by-event?eventId=${eventId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        // Filter only PAID tickets and sort by creation date (newest first)
        return response.data.body
          .filter((ticket) => ticket.ticketStatus === 'PAID')
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      }
      throw new Error(response.data.message || 'Failed to fetch tickets');
    } catch (error) {
      console.error('Error fetching event tickets:', error);
      throw error;
    }
  }

  /**
   * Transform API ticket response to UI-friendly format
   */
  transformTicket(ticketResponse: TicketResponse): Ticket {
    const { event, ticketType, price, ticketNumber, user } = ticketResponse;

    // Format date and time
    const startDate = new Date(event.startDateTime);
    const endDate = new Date(event.endDateTime);

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    };

    const formatTime = (date: Date) => {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        timeZoneName: 'short',
      });
    };

    // Generate QR code for ticket number
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=${ticketNumber}`;

    // Get event image
    const eventImage =
      event.coverImages?.[0]?.secureUrl ||
      'https://images.unsplash.com/photo-1563924595624-b73b73c5afd8';

    return {
      id: ticketResponse.id,
      eventName: event.title,
      date: formatDate(startDate),
      time: formatTime(startDate),
      endTime: formatTime(endDate),
      location: event.location,
      ticketType,
      seat: `${ticketType} Admission`,
      price: `$${price.toFixed(2)}`,
      qrCode: qrCodeUrl,
      eventImage,
      orderNumber: ticketResponse.transactionReferenceNumber,
      attendeeName: user.fullName,
      organizer: event.user.fullName,
      venue: event.location,
      eventSummary: event.description,
      ticketStatus: ticketResponse.ticketStatus,
      rawEvent: event,
    };
  }

  /**
   * Categorize tickets into upcoming and past based on event end time
   */
  categorizeTickets(tickets: TicketResponse[]): { upcoming: Ticket[]; past: Ticket[] } {
    const now = new Date();
    const upcoming: Ticket[] = [];
    const past: Ticket[] = [];

    tickets.forEach((ticket) => {
      const transformedTicket = this.transformTicket(ticket);
      const eventEndTime = new Date(ticket.event.endDateTime);

      if (eventEndTime > now) {
        upcoming.push(transformedTicket);
      } else {
        past.push(transformedTicket);
      }
    });

    // Sort by date (upcoming: soonest first, past: most recent first)
    upcoming.sort(
      (a, b) =>
        new Date(a.rawEvent.startDateTime).getTime() - new Date(b.rawEvent.startDateTime).getTime()
    );
    past.sort(
      (a, b) =>
        new Date(b.rawEvent.startDateTime).getTime() - new Date(a.rawEvent.startDateTime).getTime()
    );

    return { upcoming, past };
  }
}

export default new TicketService();
