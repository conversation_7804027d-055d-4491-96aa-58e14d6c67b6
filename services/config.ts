import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

const axiosInstance = axios.create({
  baseURL: 'http://**************:5025',
  timeout: 120000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to handle file uploads with longer timeout
axiosInstance.interceptors.request.use(
  (config) => {
    // Check if this is a file upload request
    if (
      config.headers['Content-Type']?.includes('multipart/form-data') ||
      config.data instanceof FormData
    ) {
      config.timeout = 120000; // 60 seconds for file uploads
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add an interceptor to handle token expiration
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Check if the error is due to an expired token
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const refreshToken = await SecureStore.getItemAsync('refreshToken');
        if (refreshToken) {
          const refreshResponse = await axiosInstance.post('/auth/v1/refresh-token', {
            refreshToken,
          });

          const newAccessToken = refreshResponse.data.body.accessToken;
          await SecureStore.setItemAsync('accessToken', newAccessToken);

          // Update the Authorization header and retry the original request
          originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        // Handle refresh token failure (e.g., logout user)
        await SecureStore.deleteItemAsync('accessToken');
        await SecureStore.deleteItemAsync('refreshToken');
        throw refreshError;
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
