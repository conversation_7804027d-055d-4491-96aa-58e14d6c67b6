import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';
import { FirebaseService } from './FirebaseService';

export class UserService {
  static async registerUser(userDetails: {
    fullName: string;
    email: string;
    phoneNumber: string;
    password: string;
    verificationCode: string;
    userId: string;
    termsAccepted: boolean;
  }) {
    try {
      const response = await axiosInstance.post('/auth/v1/register-user', userDetails);
      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async setupProfile(userDetails: {
    email: string;
    phoneNumber: string;
    gender: string;
    interests: string[];
    eventPreferences: string[];
    password: string;
  }) {
    try {
      let accessToken = null;
      const userdata = await SecureStore.getItemAsync('userData');

      if (userdata) {
        const userData = JSON.parse(userdata);
        accessToken = userData.accessToken;
        userDetails.email = userData.email;
        userDetails.phoneNumber = userData.phoneNumber;
      } else {
        // attempt login
        const response1 = await axiosInstance.post('/auth/v1/login', {
          email: userDetails.email,
          password: userDetails.password,
        });

        accessToken = response1.data.accessToken;
      }

      const response = await axiosInstance.post('/user/v1/profile-setup', userDetails, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getMyProfile() {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/user/v1/get-my-profile', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async updateProfile(userDetails: any) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');

      const response = await axiosInstance.put('/user/v1/update-profile', userDetails, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async getUsers() {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/user/v1/get-all-users', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch users');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static async getOtherUser(queryParams: any = {}) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.get('/user/v1/get-other-user', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: queryParams,
      });

      if (response.status !== 200) {
        throw new Error('Failed to fetch user');
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
