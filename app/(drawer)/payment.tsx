import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Alert, BackHandler } from 'react-native';
import { WebView } from 'react-native-webview';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useColorScheme } from '~/lib/useColorScheme';
import { PaymentPollingService } from '~/services/PaymentPollingService';

export default function PaymentScreen() {
  const { colors } = useColorScheme();
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const params = useLocalSearchParams();

  const webViewRef = useRef<WebView>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [canGoBack, setCanGoBack] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [paymentStatus, setPaymentStatus] = useState<'processing' | 'success' | 'failed' | null>(
    null
  );
  const [paymentMessage, setPaymentMessage] = useState('');
  const [isScreenFocused, setIsScreenFocused] = useState(false);
  const pollingRef = useRef<{ cancel: () => void; pollId: string } | null>(null);

  const paymentUrl = params.paymentUrl as string;
  const pollUrl = params.pollUrl as string;
  const eventTitle = params.eventTitle as string;
  const ticketType = params.ticketType as string;
  const from = params.from as string;
  const quantity = params.quantity as string;
  const isPromotion = params.isPromotion === 'true';

  // Track if the screen is focused
  useFocusEffect(
    useCallback(() => {
      setIsScreenFocused(true);
      return () => setIsScreenFocused(false);
    }, [])
  );

  useEffect(() => {
    // Reset payment status when component mounts or pollUrl changes
    setPaymentStatus(null);
    setPaymentMessage('');

    // Cancel any existing polling operations before starting new ones
    PaymentPollingService.cancelAllPolling();
    console.log(
      `Payment screen mounted - cancelled ${PaymentPollingService.getActivePollingCount()} existing polls`
    );

    // Start payment polling when component mounts
    if (pollUrl) {
      const polling = PaymentPollingService.startBackgroundPolling(pollUrl, (status) => {
        console.log('Payment status update:', status);
        if (status.status === 'Paid') {
          setPaymentStatus('success');
          if (isPromotion) {
            setPaymentMessage(`Successfully activated ${ticketType} promotion for ${eventTitle}`);
          } else {
            setPaymentMessage(
              `Successfully purchased ${quantity} ${ticketType} ticket${parseInt(quantity, 10) > 1 ? 's' : ''} for ${eventTitle}`
            );
          }
        } else if (status.status === 'Cancelled') {
          setPaymentStatus('failed');
          setPaymentMessage('Payment was cancelled. You can try again when ready.');
        }
      });

      // Handle the polling promise result
      polling.promise
        .then((result) => {
          console.log('Polling promise resolved:', result);
          // Only update status if this is still the current polling operation
          if (pollingRef.current?.pollId === polling.pollId) {
            if (
              result.status === 'Failed' ||
              result.status === 'Timeout' ||
              result.status === 'Cancelled'
            ) {
              setPaymentStatus('failed');
              let message = 'Payment failed. Please try again.';
              if (result.status === 'Timeout') {
                message =
                  'Payment verification timed out. Please check your payment status or try again.';
              } else if (result.status === 'Cancelled') {
                message = 'Payment was cancelled. You can try again when ready.';
              }
              setPaymentMessage(message);
            }
          } else {
            console.log('Ignoring polling result from cancelled operation:', polling.pollId);
          }
        })
        .catch((error) => {
          console.error('Polling promise error:', error);
          // Only update status if this is still the current polling operation
          if (pollingRef.current?.pollId === polling.pollId) {
            setPaymentStatus('failed');
            setPaymentMessage('An error occurred during payment verification. Please try again.');
          }
        });

      pollingRef.current = polling;
      console.log(`Started new polling operation: ${polling.pollId}`);
    }

    // Cleanup on unmount
    return () => {
      if (pollingRef.current) {
        console.log(`Cleaning up polling operation: ${pollingRef.current.pollId}`);
        pollingRef.current.cancel();
      }
    };
  }, [pollUrl, quantity, ticketType, eventTitle]);

  // Handle hardware back button
  useEffect(() => {
    const handleBackPress = () => {
      // Only disable back button if this payment screen is focused, it's a promotion, and payment is successful
      if (isScreenFocused && isPromotion && paymentStatus === 'success') {
        return true; // Prevent back navigation
      }
      handleClose();
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [isScreenFocused, isPromotion, paymentStatus]);

  const handleGoBack = () => {
    if (canGoBack && webViewRef.current) {
      webViewRef.current.goBack();
    }
  };

  const handleRefresh = () => {
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  const handleClose = () => {
    const cleanupAndClose = () => {
      if (pollingRef.current) {
        console.log(`Closing payment screen - cancelling polling: ${pollingRef.current.pollId}`);
        pollingRef.current.cancel();
        pollingRef.current = null;
      }
      // Reset payment status when closing
      setPaymentStatus(null);
      setPaymentMessage('');
      if (from == 'eventsheet') {
        router.back();
      } else {
        router.dismiss();
      }
    };

    if (paymentStatus === 'processing' || paymentStatus === null) {
      Alert.alert(
        'Close Payment',
        'Are you sure you want to close the payment? Your transaction may not be completed.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Close',
            style: 'destructive',
            onPress: cleanupAndClose,
          },
        ]
      );
    } else {
      cleanupAndClose();
    }
  };

  const handleWebViewNavigationStateChange = (navState: any) => {
    setCanGoBack(navState.canGoBack);
    setCurrentUrl(navState.url);
    setIsLoading(navState.loading);

    // Check if payment is completed based on URL
    if (navState.url.includes('success') || navState.url.includes('completed')) {
      console.log('Payment URL suggests completion:', navState.url);
      // Don't set success here, let polling handle it
    }
  };

  const handleViewTickets = () => {
    router.replace('/(drawer)/tickets');
  };

  const handleGoHome = () => {
    router.replace('/(drawer)/(tabs)');
  };

  const handleRetry = () => {
    // Cancel current polling and reset state
    if (pollingRef.current) {
      console.log(`Cancelling current polling operation: ${pollingRef.current.pollId}`);
      pollingRef.current.cancel();
      pollingRef.current = null;
    }

    // Reset payment status to allow retry
    setPaymentStatus(null);
    setPaymentMessage('');

    // Go back to the previous screen to retry the payment
    router.back();
  };

  // Show success/failure overlay
  if (paymentStatus === 'success' || paymentStatus === 'failed') {
    return (
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {/* Header */}
        <View
          className="flex-row items-center justify-between px-4 py-3 border-b"
          style={{
            borderBottomColor: colors.grey5,
            paddingTop: insets.top + 12,
          }}>
          <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
            {isPromotion ? 'Promotion' : 'Payment'}{' '}
            {paymentStatus === 'success' ? 'Successful' : 'Failed'}
          </Text>
          <TouchableOpacity onPress={handleClose} className="p-2">
            <MaterialIcons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Status Content */}
        <View className="items-center justify-center flex-1 px-6">
          <View
            className="items-center justify-center w-20 h-20 mb-6 rounded-full"
            style={{ backgroundColor: `${paymentStatus === 'success' ? '#10b981' : '#ef4444'}20` }}>
            <MaterialIcons
              name={paymentStatus === 'success' ? 'check-circle' : 'error'}
              size={40}
              color={paymentStatus === 'success' ? '#10b981' : '#ef4444'}
            />
          </View>

          <Text
            className="mb-4 text-2xl font-bold text-center"
            style={{ color: colors.foreground }}>
            {paymentStatus === 'success'
              ? isPromotion
                ? 'Promotion Successful!'
                : 'Payment Successful!'
              : isPromotion
                ? 'Promotion Failed'
                : 'Payment Failed'}
          </Text>

          <Text className="mb-8 text-base text-center" style={{ color: colors.grey }}>
            {paymentMessage}
          </Text>

          {paymentStatus === 'success' ? (
            <View className="w-full gap-3">
              {isPromotion ? (
                <>
                  <TouchableOpacity
                    onPress={handleGoHome}
                    className="items-center px-6 py-4 bg-green-500 rounded-lg">
                    <Text className="text-lg font-bold text-white">Go to Home</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <TouchableOpacity
                    onPress={handleViewTickets}
                    className="items-center px-6 py-4 bg-green-500 rounded-lg">
                    <Text className="text-lg font-bold text-white">View My Tickets</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={handleClose}
                    className="items-center px-6 py-4 border-2 rounded-lg"
                    style={{ borderColor: colors.grey }}>
                    <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
                      Back to Event
                    </Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          ) : (
            <View className="w-full gap-3">
              <TouchableOpacity
                onPress={handleRetry}
                className="items-center px-6 py-4 rounded-lg"
                style={{ backgroundColor: colors.primary }}>
                <Text className="text-lg font-bold text-white">Try Again</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleClose}
                className="items-center px-6 py-4 border-2 rounded-lg"
                style={{ borderColor: colors.grey }}>
                <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
                  Back to Event
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      {/* Header */}
      <View
        className="flex-row items-center justify-between px-4 py-3 border-b"
        style={{
          borderBottomColor: colors.grey5,
          paddingTop: insets.top + 12,
        }}>
        <View className="flex-row items-center flex-1">
          <TouchableOpacity onPress={handleClose} className="p-2 mr-3">
            <MaterialIcons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>

          <View className="flex-1">
            <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
              {isPromotion ? 'Promotion Payment' : 'Payment'}
            </Text>
            {currentUrl && (
              <Text className="text-xs" style={{ color: colors.grey }} numberOfLines={1}>
                {currentUrl}
              </Text>
            )}
          </View>
        </View>

        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={handleGoBack}
            disabled={!canGoBack}
            className={`mr-3 p-2 ${canGoBack ? '' : 'opacity-50'}`}>
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={canGoBack ? colors.foreground : colors.grey}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={handleRefresh} className="p-2">
            <MaterialIcons name="refresh" size={20} color={colors.foreground} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Loading Indicator */}
      {isLoading && (
        <View className="absolute left-0 right-0 z-10 items-center top-20">
          <View className="px-4 py-2 bg-white rounded-full shadow-lg dark:bg-gray-800">
            <ActivityIndicator size="small" color={colors.primary} />
          </View>
        </View>
      )}

      {/* WebView */}
      <View className="flex-1">
        {paymentUrl ? (
          <WebView
            ref={webViewRef}
            source={{ uri: paymentUrl }}
            onNavigationStateChange={handleWebViewNavigationStateChange}
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView error: ', nativeEvent);
              Alert.alert('Loading Error', 'Failed to load the payment page. Please try again.', [
                { text: 'Retry', onPress: handleRefresh },
                { text: 'Close', onPress: handleClose },
              ]);
            }}
            startInLoadingState
            javaScriptEnabled
            domStorageEnabled
            allowsInlineMediaPlayback
            mediaPlaybackRequiresUserAction={false}
            style={{ flex: 1 }}
            scalesPageToFit
            showsVerticalScrollIndicator
            showsHorizontalScrollIndicator={false}
          />
        ) : (
          <View className="items-center justify-center flex-1">
            <Text style={{ color: colors.grey }}>No payment URL provided</Text>
          </View>
        )}
      </View>
    </View>
  );
}
