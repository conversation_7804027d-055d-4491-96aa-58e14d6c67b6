import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet';
import { StatusBar } from 'expo-status-bar';
import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  Platform,
  SafeAreaView,
  Keyboard,
  TouchableOpacity,
  Modal,
  TextInput,
  Image,
  Dimensions,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import { Toast } from 'toastify-react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';

import ActiveChat from '../../../components/Chat/ActiveChat';
import ActiveGroupChat from '../../../components/Chat/ActiveGroupChat';
import BrowseCommunitiesModal from '../../../components/Chat/BrowseCommunitiesModal';
import ChatList from '../../../components/Chat/ChatList';
import CreateCommunityModal from '../../../components/Chat/CreateCommunityModal';
import GroupMembersModal from '../../../components/Chat/GroupMembersModal';
import NewChatSheet from '../../../components/Chat/NewChatSheet';
import { useFirebaseChat } from '../../../hooks/useFirebaseChat';
import { useFirebaseGroupChat } from '../../../hooks/useFirebaseGroupChat';
import { useColorScheme } from '../../../lib/useColorScheme';
import { CommunityService } from '../../../services/CommunityService';
import { UserStore } from '../../../store/store';
import {
  ChatItem,
  Friend,
  Message,
  GroupChatItem,
  GroupMessage,
  GroupMember,
} from '../../../types/chat_type';

// Get current user data
const getCurrentUserData = () => {
  const userData = UserStore.getState().user as any;
  return {
    _id: userData?.id || '1',
    name: userData?.fullName || userData?.username || 'Me',
    avatar:
      userData?.profilePicture || 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
  };
};

// Enum for chat types
enum ChatType {
  DIRECT = 'direct',
  COMMUNITY = 'community',
  EVENT = 'event',
}

const Chat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatType, setChatType] = useState<ChatType>(ChatType.DIRECT);
  const [selectedChat, setSelectedChat] = useState<ChatItem | null>(null);
  const [selectedGroupChat, setSelectedGroupChat] = useState<GroupChatItem | null>(null);
  // const [myCommunities, setMyCommunities] = useState(dummyCommunities);  // Commented out unused
  const [showCreateCommunityModal, setShowCreateCommunityModal] = useState(false);
  const [showBrowseCommunitiesModal, setShowBrowseCommunitiesModal] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [groupMessages, setGroupMessages] = useState<GroupMessage[]>([]);
  const [groupMembers, setGroupMembers] = useState<GroupMember[]>([]);
  const [showGroupMembersModal, setShowGroupMembersModal] = useState(false);
  const [messageListener, setMessageListener] = useState<(() => void) | null>(null);
  const [showImagePreviewModal, setShowImagePreviewModal] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [imageCaption, setImageCaption] = useState('');
  const [tempMessages, setTempMessages] = useState<any[]>([]);
  const [messagesLoading, setMessagesLoading] = useState(false);
  // const [creatingChat, setCreatingChat] = useState(false); // Removed - NewChatSheet has its own state

  // const router = useRouter();  // Commented out unused
  // const insets = useSafeAreaInsets();  // Commented out unused
  const { colors, colorScheme } = useColorScheme(); // Removed unused setColorScheme
  const isDark = colorScheme === 'dark';
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const userData = UserStore((state: any) => state.user);

  // Firebase chat hook
  const {
    chats,
    loading,
    error,
    friendsWithoutChats,
    startChat,
    sendMessage: sendFirebaseMessage,
    listenToMessages,
    refetchChats,
  } = useFirebaseChat();

  // Firebase group chat hook
  const {
    eventChats,
    communityChats,
    loading: groupLoading,
    // error: groupError,  // Commented out unused
    loadGroupChats,
    sendGroupMessage,
    listenToGroupMessages,
    // joinGroupChat,  // Commented out unused
    leaveGroupChat,
    getGroupMembers,
  } = useFirebaseGroupChat();

  // Show error toast when Firebase error occurs
  useEffect(() => {
    if (error) {
    }
  }, [error, isDark, colors]);

  // Reference for the bottom sheet
  const newChatSheetRef = useRef<BottomSheet>(null);
  // Define the snap points for the bottom sheet
  const snapPoints = useMemo(() => ['70%'], []);

  // Open the new chat bottom sheet
  const handleOpenNewChatSheet = useCallback(() => {
    newChatSheetRef.current?.expand();
  }, []);

  // Close the new chat bottom sheet
  const handleCloseNewChatSheet = useCallback(() => {
    newChatSheetRef.current?.close();
  }, []);

  // Listen to messages when a chat is selected
  useEffect(() => {
    if (selectedChat && selectedChat.chatId) {
      // Set loading to true when starting to fetch messages
      setMessagesLoading(true);

      // Clean up previous listener
      if (messageListener) {
        messageListener();
      }

      // Set up new listener
      const unsubscribe = listenToMessages(selectedChat.chatId, (newMessages) => {
        setMessages(newMessages);
        // Set loading to false once messages are received
        setMessagesLoading(false);

        // Clear all temporary messages when Firebase updates
        setTempMessages([]);
      });

      setMessageListener(() => unsubscribe);
      setCurrentChatId(selectedChat.chatId);
    } else {
      // Clean up listener when no chat is selected
      if (messageListener) {
        messageListener();
        setMessageListener(null);
      }
      setMessages([]);
      setMessagesLoading(false);
      setCurrentChatId(null);
    }

    // Cleanup on unmount
    return () => {
      if (messageListener) {
        messageListener();
      }
    };
  }, [selectedChat, listenToMessages]);

  // Listen to group messages when a group chat is selected
  useEffect(() => {
    if (selectedGroupChat && selectedGroupChat.chatId) {
      // Set loading to true when starting to fetch messages
      setMessagesLoading(true);

      // Clean up previous listener
      if (messageListener) {
        messageListener();
      }

      // Set up new listener for group messages
      const unsubscribe = listenToGroupMessages(selectedGroupChat.chatId, (newMessages) => {
        setGroupMessages(newMessages);
        // Set loading to false once messages are received
        setMessagesLoading(false);

        // Clear all temporary messages when Firebase updates
        setTempMessages([]);
      });

      setMessageListener(() => unsubscribe);
      setCurrentChatId(selectedGroupChat.chatId);
    } else if (!selectedChat) {
      // Clean up listener when no group chat is selected and no direct chat
      if (messageListener) {
        messageListener();
        setMessageListener(null);
      }
      setGroupMessages([]);
      setMessagesLoading(false);
      if (!selectedChat) {
        setCurrentChatId(null);
      }
    }

    // Cleanup on unmount
    return () => {
      if (messageListener) {
        messageListener();
      }
    };
  }, [selectedGroupChat, listenToGroupMessages, selectedChat]);

  const handleSelectChat = (chat: ChatItem, type: 'direct' | 'community' | 'event') => {
    setChatType(type as ChatType);

    if (type === 'direct') {
      setSelectedChat(chat);
      setSelectedGroupChat(null);
    } else {
      // Handle group chats (community or event)
      const groupChat = chat as GroupChatItem;
      setSelectedGroupChat(groupChat);
      setSelectedChat(null);

      // Load group members
      if (groupChat.chatId) {
        getGroupMembers(groupChat.chatId).then(setGroupMembers);
      }
    }
  };

  const handleSelectFriend = async (friend: Friend) => {
    try {
      // Start a new chat with Firebase
      const chatId = await startChat(friend.id);

      if (chatId) {
        // Create a new chat item from the friend
        const newChat: ChatItem = {
          id: friend.id,
          name: friend.name,
          avatar: friend.avatar,
          lastMessage: '',
          timestamp: 'Just now',
          chatId, // Use property shorthand
        };

        // Close the bottom sheet
        handleCloseNewChatSheet();

        // Start the chat
        handleSelectChat(newChat, 'direct');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to start chat',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      }
    } catch (error) {
      console.error('Error starting chat:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to start chat',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const handleNavigateToExistingChat = (chat: ChatItem) => {
    // Close the bottom sheet
    handleCloseNewChatSheet();

    // Navigate to the existing chat
    handleSelectChat(chat, 'direct');
  };

  const handleOpenCreateCommunity = () => {
    setShowCreateCommunityModal(true);
  };

  const handleCloseCreateCommunity = () => {
    setShowCreateCommunityModal(false);
  };

  const handleCreateCommunity = async (name: string, description: string, imageUri: string) => {
    if (!userData?.id) return;

    try {
      // Create community using the API
      const response = await CommunityService.createCommunity({
        userId: userData.id,
        communityName: name,
        communityDescription: description,
        communityImages: imageUri ? [imageUri as any] : undefined,
      });

      if (response.success) {
        setShowCreateCommunityModal(false);

        // Reload group chats to show the new community
        loadGroupChats();

        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Community created successfully!',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      }
    } catch (error) {
      console.error('Error creating community:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to create community',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const handleOpenBrowseCommunities = () => {
    setShowBrowseCommunitiesModal(true);
  };

  const handleCloseBrowseCommunities = () => {
    setShowBrowseCommunitiesModal(false);
  };

  const handleJoinCommunity = async (community: any) => {
    // Close the modal
    setShowBrowseCommunitiesModal(false);

    // Reload group chats to show the joined community
    loadGroupChats();

    // Show success toast
    Toast.show({
      type: 'success',
      text1: 'Success',
      text2: `Joined ${community.communityName} successfully!`,
      position: 'bottom',
      theme: isDark ? 'dark' : 'light',
      backgroundColor: colors.background,
      autoHide: true,
    });
  };

  const handleJoinCommunityError = (error: string) => {
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: error,
      position: 'bottom',
      theme: isDark ? 'dark' : 'light',
      backgroundColor: colors.background,
      autoHide: true,
    });
  };

  const onSend = useCallback(
    async (newMessages: any[] = []) => {
      if (!currentChatId || !selectedChat || !userData) return;

      const message = newMessages[0];
      if (!message) return;

      try {
        // Handle multiple images
        if (message.images && Array.isArray(message.images)) {
          // Create temporary messages for each image
          const tempImageMessages = message.images.map((imageUri: string, index: number) => ({
            _id: `temp_${Math.round(Math.random() * 1000000)}_${index}`,
            text: index === message.images.length - 1 && message.text ? message.text : undefined,
            createdAt: new Date(Date.now() + index),
            user: getCurrentUserData(),
            image: imageUri,
            pending: true,
            failed: false,
          }));

          // Add temporary messages to display immediately
          setTempMessages((prev) => [...tempImageMessages, ...prev]);

          // Send each image (don't wait for response, rely on Firebase listener)
          for (let i = 0; i < message.images.length; i++) {
            const imageUri = message.images[i];
            const isLastImage = i === message.images.length - 1;
            const messageText = isLastImage && message.text ? message.text : undefined;
            const tempMessage = tempImageMessages[i];

            // Send without waiting for response
            sendFirebaseMessage(currentChatId, selectedChat.id, messageText, imageUri).catch(
              (error) => {
                console.error('Error sending image:', error);
                // Mark as failed only on error
                setTempMessages((prev) =>
                  prev.map((msg) =>
                    msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
                  )
                );
              }
            );
          }
        } else {
          // Handle single message (text or single image)
          if (!message.text && !message.image) return;

          // Create temporary message
          const tempMessage = {
            ...message,
            _id: `temp_${Math.round(Math.random() * 1000000)}`,
            pending: true,
            failed: false,
          };

          // Add temporary message to display immediately
          setTempMessages((prev) => [tempMessage, ...prev]);

          // Send message (don't wait for response, rely on Firebase listener)
          sendFirebaseMessage(currentChatId, selectedChat.id, message.text, message.image).catch(
            (error) => {
              console.error('Error sending message:', error);
              // Mark as failed only on error
              setTempMessages((prev) =>
                prev.map((msg) =>
                  msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
                )
              );
            }
          );
        }
      } catch (error) {
        console.error('Error sending message:', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Failed to send message',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      }
    },
    [currentChatId, selectedChat, userData, sendFirebaseMessage, isDark, colors]
  );

  const handleRetryMessage = useCallback(
    async (tempMessage: any) => {
      if (!currentChatId || !selectedChat) return;

      // Mark as pending again
      setTempMessages((prev) =>
        prev.map((msg) =>
          msg._id === tempMessage._id ? { ...msg, pending: true, failed: false } : msg
        )
      );

      // Retry sending (don't wait for response, rely on Firebase listener)
      sendFirebaseMessage(
        currentChatId,
        selectedChat.id,
        tempMessage.text,
        tempMessage.image
      ).catch((error) => {
        console.error('Error retrying message:', error);
        // Mark as failed again only on error
        setTempMessages((prev) =>
          prev.map((msg) =>
            msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
          )
        );
      });
    },
    [currentChatId, selectedChat, sendFirebaseMessage]
  );

  const handleCloseImagePreview = useCallback(() => {
    setShowImagePreviewModal(false);
    setSelectedImages([]);
    setImageCaption('');
  }, []);

  const handleRemoveImage = useCallback((index: number) => {
    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Group chat message sending
  const onGroupSend = useCallback(
    (messages: any[]) => {
      if (!selectedGroupChat || !currentChatId) return;

      for (const message of messages) {
        if (!message.text && !message.images) return;

        // Create temporary message
        const tempMessage = {
          ...message,
          _id: `temp_${Math.round(Math.random() * 1000000)}`,
          pending: true,
          failed: false,
          groupId: currentChatId,
        };

        // Add temporary message to display immediately
        setTempMessages((prev) => [tempMessage, ...prev]);

        // Send group message
        sendGroupMessage(currentChatId, message.text, message.images).catch((error) => {
          console.error('Error sending group message:', error);
          // Mark as failed only on error
          setTempMessages((prev) =>
            prev.map((msg) =>
              msg._id === tempMessage._id ? { ...msg, pending: false, failed: true } : msg
            )
          );
        });
      }
    },
    [selectedGroupChat, currentChatId, sendGroupMessage]
  );

  const handleSendImagesWithCaption = useCallback(() => {
    if (selectedImages.length === 0) return;

    // Create a message with multiple images
    const newMessage = {
      _id: Math.round(Math.random() * 1000000).toString(),
      createdAt: new Date(),
      user: getCurrentUserData(),
      images: selectedImages, // Send as array
      text: imageCaption.trim() || undefined,
    };

    // Use onGroupSend if in a group chat, otherwise use onSend
    if (selectedGroupChat) {
      onGroupSend([newMessage]);
    } else {
      onSend([newMessage]);
    }

    // Reset modal state
    setShowImagePreviewModal(false);
    setSelectedImages([]);
    setImageCaption('');
  }, [selectedImages, imageCaption, onSend, onGroupSend, selectedGroupChat]);

  // Group chat handlers
  const handleViewGroupMembers = () => {
    setShowGroupMembersModal(true);
  };

  const handleLeaveGroup = async () => {
    if (!selectedGroupChat?.chatId) return;

    try {
      // Check if this is a community chat
      if (chatType === ChatType.COMMUNITY) {
        // Use community service for community chats
        await CommunityService.leaveCommunity(selectedGroupChat.communityId, userData?.id);
      } else {
        // Use Firebase for other group chats
        await leaveGroupChat(selectedGroupChat.chatId);
      }

      setSelectedGroupChat(null);
      setGroupMessages([]);
      setGroupMembers([]);
      // Reload group chats to reflect the change
      loadGroupChats();
      Toast.success('Successfully left the group');
    } catch (error) {
      console.error('Error leaving group:', error);
      Toast.error('Failed to leave group');
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!selectedGroupChat?.chatId) return;

    try {
      // Only allow removal in community chats and if user is admin
      if (chatType === ChatType.COMMUNITY && selectedGroupChat?.groupCreatedBy === userData?.id) {
        console.log('remove user from community', memberId, selectedGroupChat);
        await CommunityService.removeUserFromCommunity(memberId, selectedGroupChat.communityId);

        // Update local group members list
        setGroupMembers((prev) => prev.filter((member) => member.id !== memberId));
        Toast.success('Member removed successfully');
      }
    } catch (error) {
      console.error('Error removing member:', error);
      Toast.error('Failed to remove member');
    }
  };

  const handleLeaveCommunityFromModal = async () => {
    setShowGroupMembersModal(false);
    await handleLeaveGroup();
  };

  // Chat actions bottom sheet ref
  const chatActionsSheetRef = useRef<BottomSheet>(null);
  const chatActionsSnapPoints = useMemo(() => ['50%'], []);

  // Handle opening chat actions bottom sheet
  const handleOpenChatActions = useCallback(() => {
    Keyboard.dismiss();
    chatActionsSheetRef.current?.expand();
  }, []);

  // Handle closing chat actions bottom sheet
  const handleCloseChatActions = useCallback(() => {
    chatActionsSheetRef.current?.close();
  }, []);

  // Handle choose from library
  const handleChooseFromLibrary = useCallback(async () => {
    try {
      const image = await ImagePicker.openPicker({
        mediaType: 'photo',
        compressImageQuality: 0.8,
        width: 400,
        height: 400,
        cropping: true,
        freeStyleCropEnabled: true,
        includeBase64: false,
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      setSelectedImages([image.path]);
      setShowImagePreviewModal(true);
      handleCloseChatActions();
    } catch (error) {
      console.log('Error picking images:', error);
    }
  }, [colors, setSelectedImages, setShowImagePreviewModal, handleCloseChatActions]);

  // Handle take photo
  const handleTakePhoto = useCallback(async () => {
    try {
      const image = await ImagePicker.openCamera({
        mediaType: 'photo',
        compressImageQuality: 0.8,
        width: 400,
        height: 400,
        cropping: true,
        freeStyleCropEnabled: true,
        includeBase64: false,
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      setSelectedImages([image.path]);
      setShowImagePreviewModal(true);
      handleCloseChatActions();
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        alert('Sorry, we need camera permissions to make this work!');
      }
    }
  }, [setSelectedImages, setShowImagePreviewModal, handleCloseChatActions]);

  // Handle record video
  const handleRecordVideo = useCallback(async () => {
    try {
      const video = await ImagePicker.openCamera({
        mediaType: 'video',
        compressVideoPreset: 'MediumQuality',
        includeBase64: false,
      });

      const newMessage = {
        _id: Math.round(Math.random() * 1000000).toString(),
        createdAt: new Date(),
        user: getCurrentUserData(),
        video: video.path,
      };
      onSend([newMessage]);
      handleCloseChatActions();
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        alert('Sorry, we need camera permissions to make this work!');
      }
    }
  }, [onSend, handleCloseChatActions]);

  const renderActions = (props: any) => {
    return (
      <TouchableOpacity
        onPress={handleOpenChatActions}
        style={{
          marginLeft: 5,
          marginBottom: 8,
          justifyContent: 'center',
          alignItems: 'center',
          height: 36,
          width: 36,
          backgroundColor: colors.background,
        }}>
        <Ionicons name="add-circle-outline" size={40} color={colors.primary} />
      </TouchableOpacity>
    );
  };

  // Combine real messages with temporary messages
  const allMessages = useMemo(() => {
    return [...tempMessages, ...messages];
  }, [tempMessages, messages]);

  // Tabs component
  const renderTabs = () => {
    return (
      <View
        className="flex-row justify-around border-b"
        style={{ borderBottomColor: 'rgba(150, 150, 150, 0.2)' }}>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.DIRECT ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.DIRECT)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.DIRECT ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.DIRECT ? colors.foreground : colors.grey }}>
            DIRECT
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.EVENT ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.EVENT)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.EVENT ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.EVENT ? colors.foreground : colors.grey }}>
            EVENTS
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 items-center border-b-2 py-3.5 ${chatType === ChatType.COMMUNITY ? 'border-violet-600' : 'border-transparent'}`}
          onPress={() => setChatType(ChatType.COMMUNITY)}>
          <Text
            className={`font-medium text-xs ${chatType === ChatType.COMMUNITY ? 'opacity-100' : 'opacity-70'}`}
            style={{ color: chatType === ChatType.COMMUNITY ? colors.foreground : colors.grey }}>
            COMMUNITIES
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.5} />
    ),
    []
  );

  // Render chat lists based on type
  const renderChatLists = () => {
    let data = [];
    let renderType = 'direct';
    let headerTitle = '';
    let showAddButton = false;

    switch (chatType) {
      case ChatType.DIRECT:
        data = chats; // Use Firebase chats
        renderType = 'direct';
        headerTitle = 'Direct Messages';
        showAddButton = true;
        break;
      case ChatType.COMMUNITY:
        data = communityChats; // Use Firebase community chats
        renderType = 'community';
        headerTitle = 'Communities';
        break;
      case ChatType.EVENT:
        data = eventChats; // Use Firebase event chats
        renderType = 'event';
        headerTitle = 'Event Chats';
        break;
    }

    return (
      <View className="flex-1" style={{ backgroundColor: colors.background }}>
        <View className="mt-1" />

        {renderTabs()}

        {/* Show loader when loading chats */}
        {(loading && chatType === ChatType.DIRECT) ||
        (groupLoading && (chatType === ChatType.COMMUNITY || chatType === ChatType.EVENT)) ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="mt-4 text-base opacity-70" style={{ color: colors.foreground }}>
              Loading chats...
            </Text>
          </View>
        ) : (
          <ChatList
            data={data}
            type={renderType as 'direct' | 'community' | 'event'}
            onSelect={handleSelectChat}
            isDark={isDark}
            colors={colors}
            headerTitle={headerTitle}
            showAddButton={showAddButton}
            onAddPress={
              renderType === 'community' ? handleOpenCreateCommunity : handleOpenNewChatSheet
            }
            onBrowsePress={handleOpenBrowseCommunities}
          />
        )}
      </View>
    );
  };

  // Get URL parameters
  const params = useLocalSearchParams();
  const router = useRouter();

  // Handle auto chat start from user profile
  useEffect(() => {
    const profileUserId = params.userId?.toString();
    const profileUserName = params.name?.toString();
    const shouldStartNewChat = params.startNewChat === 'true';

    if (profileUserId && shouldStartNewChat && !selectedChat && !selectedGroupChat) {
      // First check if we already have a chat with this user
      const existingChat = chats.find((chat) => chat.id === profileUserId);

      if (existingChat) {
        // If chat exists, just select it
        handleSelectChat(existingChat, 'direct');
      } else if (!loading) {
        // If no existing chat, create new friend object and start chat
        const friend: Friend = {
          id: profileUserId,
          name: profileUserName || 'User',
          avatar: '',
          status: 'offline', // Required field in Friend type
        };

        // Use the existing handleSelectFriend function to create chat
        handleSelectFriend(friend);
      }
    }
  }, [
    params,
    chats,
    loading,
    selectedChat,
    selectedGroupChat,
    handleSelectChat,
    handleSelectFriend,
  ]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () =>
      setKeyboardVisible(true)
    );
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () =>
      setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <SafeAreaView
      className="flex-1 pt-12"
      style={{
        backgroundColor: colors.background,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      {selectedChat || selectedGroupChat ? (
        <></>
      ) : (
        <View className="flex-row items-center justify-center px-4 pb-4">
          <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Messages
          </Text>
        </View>
      )}
      {selectedChat ? (
        <ActiveChat
          selectedChat={selectedChat}
          messages={allMessages}
          onSend={onSend}
          onBack={() => {
            setSelectedChat(null);
            router.replace('/(drawer)/(tabs)/chat');
          }}
          renderActions={renderActions}
          colors={colors}
          isDark={isDark}
          user={getCurrentUserData()}
          onRetryMessage={handleRetryMessage}
          messagesLoading={messagesLoading}
        />
      ) : selectedGroupChat ? (
        <ActiveGroupChat
          selectedGroupChat={selectedGroupChat}
          messages={groupMessages}
          onSend={onGroupSend}
          onBack={() => {
            setSelectedGroupChat(null);
            setGroupMessages([]);
            setGroupMembers([]);
          }}
          renderActions={renderActions}
          colors={colors}
          isDark={isDark}
          user={getCurrentUserData()}
          onRetryMessage={handleRetryMessage}
          messagesLoading={messagesLoading}
          groupMembers={groupMembers}
          onViewMembers={handleViewGroupMembers}
          onLeaveGroup={handleLeaveGroup}
        />
      ) : (
        renderChatLists()
      )}

      {/* Image Preview Modal */}
      <Modal
        visible={showImagePreviewModal}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={handleCloseImagePreview}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={0}>
          <View style={{ flex: 1, backgroundColor: 'black' }}>
            {/* Header */}
            <SafeAreaView>
              <View className="flex-row items-center justify-between px-4 py-2">
                <TouchableOpacity onPress={handleCloseImagePreview}>
                  <Ionicons name="close" size={24} color="white" />
                </TouchableOpacity>
                <Text className="font-medium text-white">
                  Send {selectedImages.length} {selectedImages.length === 1 ? 'Image' : 'Images'}
                </Text>
                <View style={{ width: 24 }} />
              </View>
            </SafeAreaView>

            {/* Images Preview */}
            <View className="flex-1 items-center justify-center">
              {selectedImages.length === 1 ? (
                // Single image - full screen
                <Image
                  source={{ uri: selectedImages[0] }}
                  style={{
                    width: Dimensions.get('window').width,
                    height: Dimensions.get('window').height * 0.7,
                  }}
                  resizeMode="contain"
                />
              ) : (
                // Multiple images - grid view
                <View style={{ flex: 1, paddingHorizontal: 16 }}>
                  <Text className="mb-4 text-center text-white">
                    {selectedImages.length} images selected
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      flexWrap: 'wrap',
                      justifyContent: 'center',
                      gap: 8,
                    }}>
                    {selectedImages.map((uri, index) => (
                      <View key={index} style={{ position: 'relative' }}>
                        <Image
                          source={{ uri }}
                          style={{
                            width: 100,
                            height: 100,
                            borderRadius: 8,
                          }}
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          onPress={() => handleRemoveImage(index)}
                          style={{
                            position: 'absolute',
                            top: -8,
                            right: -8,
                            backgroundColor: 'red',
                            borderRadius: 12,
                            width: 24,
                            height: 24,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <Ionicons name="close" size={16} color="white" />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>

            {/* Caption Input and Send Button */}
            <SafeAreaView>
              <View
                className="flex-row items-end px-4 py-2"
                style={{
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  marginBottom: keyboardVisible ? 23 : 0,
                }}>
                <View className="mr-3 flex-1">
                  <TextInput
                    value={imageCaption}
                    onChangeText={setImageCaption}
                    placeholder="Add a caption..."
                    placeholderTextColor="#999"
                    multiline
                    maxLength={500}
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      borderRadius: 20,
                      paddingHorizontal: 16,
                      paddingVertical: 12,
                      color: 'white',
                      fontSize: 16,
                      maxHeight: 100,
                    }}
                  />
                </View>
                <TouchableOpacity
                  onPress={handleSendImagesWithCaption}
                  className="h-12 w-12 items-center justify-center rounded-full"
                  style={{ backgroundColor: colors.primary }}>
                  <Ionicons name="send" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </SafeAreaView>
          </View>
        </KeyboardAvoidingView>
      </Modal>

      {/* Bottom Sheet for New Chat */}
      <BottomSheet
        ref={newChatSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <NewChatSheet
          bottomSheetRef={newChatSheetRef}
          onSelectFriend={handleSelectFriend}
          colors={colors}
          isDark={isDark}
          friendsWithoutChats={friendsWithoutChats}
          isOpen
          onClose={handleCloseNewChatSheet}
          existingChats={chats}
          onNavigateToChat={handleNavigateToExistingChat}
        />
      </BottomSheet>

      {/* Modal for Creating Community */}
      <CreateCommunityModal
        visible={showCreateCommunityModal}
        onClose={handleCloseCreateCommunity}
        onCreateCommunity={handleCreateCommunity}
        colors={colors}
        isDark={isDark}
      />

      {/* Modal for Browsing Communities */}
      <BrowseCommunitiesModal
        visible={showBrowseCommunitiesModal}
        onClose={handleCloseBrowseCommunities}
        onJoinCommunity={handleJoinCommunity}
        onJoinError={handleJoinCommunityError}
        colors={colors}
      />

      {/* Group Members Modal */}
      <GroupMembersModal
        isVisible={showGroupMembersModal}
        onClose={() => setShowGroupMembersModal(false)}
        members={groupMembers}
        groupName={selectedGroupChat?.name || ''}
        currentUserId={userData?.id || ''}
        canManageMembers={selectedGroupChat?.groupCreatedBy === userData?.id}
        groupType={chatType === ChatType.COMMUNITY ? 'COMMUNITY' : 'EVENT'}
        onRemoveMember={handleRemoveMember}
        onLeaveCommunity={handleLeaveCommunityFromModal}
      />

      {/* Chat Actions Bottom Sheet */}
      <BottomSheet
        ref={chatActionsSheetRef}
        index={-1}
        snapPoints={chatActionsSnapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView className="flex-1 px-5 pt-2">
          <View className="mb-5 flex-row items-center justify-between">
            <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              Chat Actions
            </Text>
            <TouchableOpacity className="p-2" onPress={handleCloseChatActions}>
              <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
          </View>

          <View className="flex gap-3">
            <TouchableOpacity
              onPress={handleChooseFromLibrary}
              className="h-14 flex-row items-center rounded-lg px-4 py-3"
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons
                name="images"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
              <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
                Choose From Library
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleTakePhoto}
              className="h-14 flex-row items-center rounded-lg px-4 py-3"
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons
                name="camera"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
              <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
                Take a Photo
              </Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </SafeAreaView>
  );
};

export default Chat;
