import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { MaterialIcons, FontAwesome5, Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { EventService } from '~/services/EventService';
import { PaymentPollingService } from '~/services/PaymentPollingService';
import { UserStore } from '~/store/store';

// Define package types
type PackageFeature = {
  icon: string;
  text: string;
};

type PromotionPackage = {
  id: string;
  name: string;
  price: string;
  duration: string;
  popular?: boolean;
  features: PackageFeature[];
};

export default function PromoteEventScreen() {
  const router = useRouter();
  const { colors, isDark } = useColorScheme();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const eventId = params.eventId as string;
  const [selectedPackage, setSelectedPackage] = useState<string>('premium');
  const [isProcessing, setIsProcessing] = useState(false);
  const currentUser = UserStore((state: any) => state.user);

  // Promotion packages data
  const packages: PromotionPackage[] = [
    {
      id: 'basic',
      name: 'Basic Package',
      price: '$9.99',
      duration: '48 hours',
      features: [
        { icon: 'map', text: 'Enhanced map visibility for 48 hours' },
        { icon: 'image', text: 'Upload up to 5 story photos' },
        { icon: 'time', text: 'Promoted stories appear for up to 48 hours' },
        { icon: 'notifications', text: 'Targeted notifications pushed to up to 200 users' },
        { icon: 'star', text: 'Subtle visual highlight on event listing' },
        { icon: 'bookmark', text: 'Featured in "Featured Events" section for 48 hours' },
      ],
    },
    {
      id: 'premium',
      name: 'Premium Package',
      price: '$24.99',
      duration: '14 days',
      popular: true,
      features: [
        { icon: 'map', text: 'Enhanced map visibility for 14 days' },
        { icon: 'image', text: 'Upload up to 10 story photos' },
        { icon: 'time', text: 'Promoted stories appear for up to 14 days' },
        { icon: 'notifications', text: 'Targeted notifications pushed to up to 2,000 users' },
        { icon: 'play-circle', text: 'Event appears in user feeds as stories for 14 days' },
        { icon: 'alarm', text: 'Automated countdown reminders for interested users' },
      ],
    },
    {
      id: 'enterprise',
      name: 'Enterprise Package',
      price: '$149.99',
      duration: 'Until event ends',
      features: [
        { icon: 'map', text: 'Permanent enhanced visibility until event ends' },
        { icon: 'image', text: 'Upload up to 20 story photos' },
        { icon: 'infinite', text: 'Promoted stories with unlimited duration' },
        { icon: 'notifications', text: 'Targeted notifications pushed to up to 20,000 users' },
        { icon: 'globe', text: 'Global reach with interest-based relevance filtering' },
        { icon: 'trophy', text: 'Top placement in search results and recommendations' },
        { icon: 'play-circle', text: 'Event appears in all user feeds as sponsored content' },
      ],
    },
  ];

  const handleSelectPackage = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handleStartPromotion = async () => {
    if (!currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Authentication Required',
        text2: 'Please log in to promote your event',
        position: 'bottom',
      });
      return;
    }

    if (!eventId) {
      Toast.show({
        type: 'error',
        text1: 'Event Not Found',
        text2: 'Unable to find the event to promote',
        position: 'bottom',
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await EventService.promoteEvent({
        eventId,
        promotionalPackage: selectedPackage.toLocaleUpperCase(),
      });

      if (response.success && response.body?.redirectUrl && response.body?.pollUrl) {
        // Cancel any existing polling operations before navigating
        PaymentPollingService.cancelAllPolling();
        console.log('Cancelled existing polls before navigating to payment');

        // Get selected package details for display
        const selectedPkg = packages.find((pkg) => pkg.id === selectedPackage);

        // Navigate to payment page
        router.push({
          pathname: '/(drawer)/payment',
          params: {
            paymentUrl: response.body.redirectUrl,
            pollUrl: response.body.pollUrl,
            eventTitle: 'Event Promotion', // You might want to fetch actual event title
            ticketType: selectedPkg?.name || selectedPackage,
            quantity: '1',
            isPromotion: 'true', // Flag to indicate this is a promotion payment
            from: 'promoteevent',
          },
        });
      } else {
        throw new Error(response.message || 'Failed to initiate promotion payment');
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Promotion Failed',
        text2: error.message || 'Failed to start promotion. Please try again.',
        position: 'bottom',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: colors.background,
        paddingTop: Platform.OS === 'ios' ? 0 : insets.top,
      }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        {/* Header with back button */}
        <View className="flex-row items-center justify-between px-4 pt-12">
          <TouchableOpacity onPress={() => router.back()} className="p-2">
            <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>

          <Text className="font-bold text-xl" style={{ color: colors.foreground }}>
            Promote Your Event
          </Text>

          <View style={{ width: 28 }} />
        </View>

        {/* Title section */}
        <View className="px-6 pt-6">
          <Text className="font-bold text-3xl" style={{ color: colors.foreground }}>
            Choose A Package
          </Text>
        </View>

        {/* Package selection */}
        <View className="px-6 pt-8">
          {packages.map((pkg) => (
            <TouchableOpacity
              key={pkg.id}
              className={`mb-4 overflow-hidden rounded-xl border-2 ${
                selectedPackage === pkg.id
                  ? 'border-violet-600'
                  : isDark
                    ? 'border-gray-700'
                    : 'border-gray-200'
              } ${isDark ? 'bg-gray-800' : 'bg-white'}`}
              onPress={() => handleSelectPackage(pkg.id)}>
              <View className="flex-row items-center justify-between p-4">
                <View className="flex-1">
                  <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
                    {pkg.name}
                  </Text>
                  <Text className="text-sm" style={{ color: colors.grey }}>
                    {pkg.price} • {pkg.duration}
                  </Text>
                </View>

                <View
                  className={`h-6 w-6 items-center justify-center rounded-full ${
                    selectedPackage === pkg.id
                      ? 'bg-violet-600'
                      : isDark
                        ? 'border border-gray-600'
                        : 'border border-gray-300'
                  }`}>
                  {selectedPackage === pkg.id && (
                    <MaterialIcons name="check" size={16} color="#fff" />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Selected package features */}
        <View className="mt-4 px-6">
          <Text className="mb-4 font-bold text-xl" style={{ color: colors.foreground }}>
            {packages.find((p) => p.id === selectedPackage)?.name} includes:
          </Text>

          {packages
            .find((p) => p.id === selectedPackage)
            ?.features.map((feature, index) => (
              <View key={index} className="mb-4 flex-row">
                <View className="mr-3 mt-1">
                  <Ionicons name="checkmark" size={18} color={isDark ? '#a78bfa' : '#7c3aed'} />
                </View>
                <View className="flex-1">
                  <Text className="text-base" style={{ color: colors.foreground }}>
                    {feature.text}
                  </Text>
                </View>
              </View>
            ))}
        </View>

        {/* Transaction Charges Disclaimer */}
        <View className="mt-6 px-6">
          <View className="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-900/20">
            <View className="flex-row items-start gap-2">
              <MaterialIcons name="info" size={16} color="#f59e0b" />
              <Text className="flex-1 text-xs text-yellow-800 dark:text-yellow-200">
                <Text className="font-medium">Note:</Text> The displayed amount does not include
                transaction charges. Additional fees may apply during payment processing.
              </Text>
            </View>
          </View>
        </View>

        {/* Start promotion button */}
        <View className="mt-2 px-6">
          <TouchableOpacity
            className={`w-full rounded-full py-4 ${isProcessing ? 'bg-gray-500 opacity-50' : 'bg-red-500'}`}
            onPress={handleStartPromotion}
            disabled={isProcessing}>
            <Text className="text-center font-bold text-lg text-white">
              {isProcessing ? 'Processing...' : 'Start Promotion'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
