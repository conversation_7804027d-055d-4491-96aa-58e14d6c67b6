import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Text,
  StyleSheet,
  Keyboard,
  KeyboardAvoidingView,
  ActivityIndicator,
  Platform,
} from 'react-native';

import { useNavigation, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import {
  GiftedChat,
  Bubble,
  Send,
  InputToolbar,
  Actions,
  MessageImage,
  Day,
  Composer,
} from 'react-native-gifted-chat';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';

import { ChatItem, Message } from '../../types/chat_type';
import PhotoViewModal from '../Profile/PhotoViewModal';
import { FileService } from '~/services/FileService';

interface ActiveChatProps {
  selectedChat: ChatItem;
  messages: Message[];
  onSend: (messages: any) => void;
  onBack: () => void;
  renderActions?: any;
  colors: any;
  isDark: boolean;
  user: {
    _id: number;
    name: string;
    avatar: string;
  };
  renderSend?: any;
  renderInputToolbar?: any;
  renderComposer?: any;
  messagesLoading?: boolean;
  onRetryMessage?: (message: any) => void;
}

interface RenderProps {
  text?: string;
  onSend?: (message: any, shouldReset: boolean) => void;
  textInputProps?: any;
  [key: string]: any;
}

const ActiveChat = ({
  selectedChat,
  messages,
  onSend,
  onBack,
  renderActions,
  colors,
  isDark,
  user,
  renderSend: customRenderSend,
  renderInputToolbar: customRenderInputToolbar,
  renderComposer: customRenderComposer,
  messagesLoading = false,
  onRetryMessage,
}: ActiveChatProps) => {
  const [onFocus, setOnFocus] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<{ id: string; secureUrl: string } | null>(
    null
  );
  const [hasStory, setHasStory] = useState(false);
  const insets = useSafeAreaInsets();

  const renderFooter = (props) => {
    return <View {...props} height={40}></View>;
  };

  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const navigation = useNavigation();
  const router = useRouter();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  useLayoutEffect(() => {
    navigation.setOptions({
      tabBarStyle: { display: 'none' },
    });

    return () => {
      navigation.setOptions({
        tabBarStyle: { display: 'flex' },
      });
    };
  }, [navigation]);

  // Check if the other user has stories
  useEffect(() => {
    const checkUserStories = async () => {
      // For one-on-one chats, try to get the other user's ID from the chat ID
      // This assumes the chat ID might contain the other user's ID or we need to extract it
      const otherUserId = extractOtherUserId(selectedChat.id, user._id);

      if (!otherUserId) return;

      try {
        const response = await FileService.getUserStories(otherUserId.toString());

        if (
          response.success &&
          response.body &&
          Array.isArray(response.body) &&
          response.body.length > 0
        ) {
          // Check if user has active stories
          const activeStories = response.body.some(
            (story: any) =>
              story.storyImages && Array.isArray(story.storyImages) && story.storyImages.length > 0
          );
          setHasStory(activeStories);
        } else {
          setHasStory(false);
        }
      } catch (error) {
        console.error('Error checking user stories:', error);
        setHasStory(false);
      }
    };

    checkUserStories();
  }, [selectedChat.id, user._id]);

  // Helper function to extract other user ID from chat ID
  const extractOtherUserId = (chatId: string, currentUserId: string | number): string | null => {
    // This is a simple implementation - you may need to adjust based on your chat ID structure
    // For now, assume the chat ID is the other user's ID (for direct chats)
    // or implement your own logic to extract the other user's ID
    if (chatId && chatId !== currentUserId.toString()) {
      return chatId;
    }
    return null;
  };

  const handleProfileImagePress = () => {
    const otherUserId = extractOtherUserId(selectedChat.id, user._id);

    if (hasStory && otherUserId) {
      // Navigate to story view
      router.push({
        pathname: '/story/view_other',
        params: { userId: otherUserId },
      });
    } else {
      // Show photo modal
      const imageData = {
        id: selectedChat.id || 'profile-image',
        secureUrl: Array.isArray(selectedChat.avatar)
          ? selectedChat.avatar[selectedChat.avatar.length - 1]?.secureUrl || selectedChat.avatar[0]
          : selectedChat.avatar,
        publicId: selectedChat.id || 'profile-photo',
      };
      setSelectedImage(imageData);
      setIsModalVisible(true);
    }
  };

  const renderBubble = (props: RenderProps) => {
    return (
      <Bubble
        {...props}
        wrapperStyle={{
          right: {
            backgroundColor: colors.primary,
            borderRadius: 18,
            paddingHorizontal: 2,
          },
          left: {
            backgroundColor: isDark ? colors.grey5 : colors.grey6,
            borderRadius: 18,
            paddingHorizontal: 2,
          },
        }}
        textStyle={{
          right: {
            color: 'white',
          },
          left: {
            color: colors.foreground,
          },
        }}
        bottomContainerStyle={{
          right: {
            marginBottom: 4,
          },
          left: {
            marginBottom: 4,
          },
        }}
      />
    );
  };

  const defaultRenderSend = (props: RenderProps) => {
    return (
      <Send
        {...props}
        containerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          alignSelf: 'center',
          marginRight: 8,
          marginBottom: 0,
          height: 36,
          width: 36,
          borderRadius: 18,
          backgroundColor: colors.primary,
        }}>
        <Ionicons name="send" size={20} color="white" />
      </Send>
    );
  };

  const defaultRenderInputToolbar = (props: RenderProps) => {
    return (
      <InputToolbar
        {...props}
        containerStyle={{
          backgroundColor: isDark ? colors.grey6 : colors.root,
          borderTopColor: colors.grey5,
          borderTopWidth: 0.5,
          padding: 6,
        }}
        primaryStyle={{
          borderRadius: 20,
          backgroundColor: isDark ? colors.grey5 : colors.grey6,
          paddingHorizontal: 12,
          marginHorizontal: 8,
        }}
      />
    );
  };

  const renderComposer = (props: RenderProps) => {
    return (
      <View
        className="flex-row items-center border-t border-gray-400 pt-2 "
        style={{
          backgroundColor: colors.background,
          borderColor: colors.grey5,
          paddingBottom:
            Platform.OS === 'ios' ? 0 : isKeyboardVisible ? verticalScale(30) : verticalScale(50),
        }}>
        {renderActions(props)}
        <Composer
          {...props}
          textInputProps={{
            onFocus: () => setOnFocus(true),
            onBlur: () => setOnFocus(false),
            blurOnSubmit: true,
            paddingVertical: 0,
            paddingHorizontal: 15,
            backgroundColor: colors.grey5,
            borderRadius: 30,
            borderColor: colors.grey5,
            borderWidth: 1,
            width: '80%',
            minHeight: 44,
            marginTop: 3,
            marginRight: 10,
            color: colors.foreground,
          }}></Composer>
        <Send {...props}>
          <View
            style={{
              justifyContent: 'center',
              height: '100%',
              marginRight: moderateScale(10),
              marginBottom: moderateScale(5),
              backgroundColor: '#4a4de7',
              width: 36,
              height: 36,
              borderRadius: 18,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Ionicons name="send-sharp" size={18} color={'white'} />
          </View>
        </Send>
      </View>
    );
  };

  const renderMessageImage = (props: RenderProps) => {
    const { currentMessage } = props;
    if (currentMessage && currentMessage.image) {
      return (
        <TouchableOpacity
          onPress={() => {
            setSelectedImage({
              id: currentMessage._id || 'image',
              secureUrl: currentMessage.uploadUrl || currentMessage.image,
            });
            setIsModalVisible(true);
          }}>
          <Image
            source={{ uri: currentMessage.image }}
            style={{
              width: 150,
              height: 100,
              borderRadius: 13,
              margin: 3,
            }}
          />
        </TouchableOpacity>
      );
    }
    return null;
  };

  const renderDay = (props: RenderProps) => {
    return (
      <Day
        createdAt={0}
        {...props}
        textStyle={{
          color: colors.grey,
          fontSize: 12,
        }}
        containerStyle={{
          marginVertical: 10,
        }}
      />
    );
  };

  const renderChatEmpty = () => {
    return (
      <View
        style={[
          styles.emptyChatContainer,
          { transform: Platform.OS === 'ios' ? [] : [{ scaleY: 1 }, { rotateY: '-180deg' }] },
        ]}>
        <View style={styles.emptyChatContent}>
          <View style={styles.suggestionCards}>
            <View
              style={[
                styles.card,
                { backgroundColor: isDark ? colors.grey5 : '#ffffff', borderColor: colors.grey5 },
                { transform: [{ scaleY: -1 }] },
              ]}>
              <Text style={[styles.cardTitle, { color: colors.foreground }]}>Say Hello</Text>
              <Text style={[styles.cardDescription, { color: isDark ? colors.grey : '#666666' }]}>
                Start a conversation by introducing yourself and greeting them.
              </Text>
            </View>
            <Text
              style={[
                styles.emptyChatTitle,
                { color: colors.primary },
                { transform: [{ scaleY: -1 }] },
              ]}>
              Start Chatting with {selectedChat.name}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderLoadingMessages = () => {
    return (
      <View
        style={[
          styles.loadingContainer,
          {
            transform: Platform.OS === 'ios' ? [{ scaleY: -1 }] : [{ scaleY: -1 }],
          },
        ]}>
        <View style={styles.loadingContent}>
          <ActivityIndicator
            style={[
              {
                transform: Platform.OS === 'ios' ? [] : [{ rotateY: '180deg' }],
              },
            ]}
            size="large"
            color={colors.primary}
          />
          <Text
            style={[
              styles.loadingText,
              { color: colors.grey },
              {
                transform: Platform.OS === 'ios' ? [] : [{ rotateY: '180deg' }],
              },
            ]}>
            Loading messages...
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View
      className="h-full flex-1"
      style={{
        backgroundColor: colors.background,
        paddingBottom: isKeyboardVisible ? moderateScale(0) : moderateScale(0),
      }}>
      <View
        className="h-[50px] flex-row items-center border-b px-4"
        style={{
          backgroundColor: colors.background,
          borderBottomColor: colors.grey5,
        }}>
        <TouchableOpacity className="p-1.5" onPress={onBack} activeOpacity={0.7}>
          <Ionicons name="arrow-back" size={24} color={colors.primary} />
        </TouchableOpacity>
        <View className="ml-2.5 flex-1 flex-row items-center">
          <TouchableOpacity onPress={handleProfileImagePress} activeOpacity={0.8}>
            <View
              className={`p-0.5 ${hasStory ? 'rounded-full border-2 border-violet-600 ' : 'bg-transparent'}`}>
              <Image
                source={{
                  uri: Array.isArray(selectedChat.avatar)
                    ? selectedChat.avatar[selectedChat.avatar.length - 1]?.secureUrl ||
                      selectedChat.avatar[0]
                    : selectedChat.avatar,
                }}
                className={`h-8 w-8 rounded-full ${hasStory ? 'border-2 border-white' : ''}`}
              />
            </View>
          </TouchableOpacity>
          <Text
            className="ml-2.5 flex-1 text-lg font-semibold"
            style={{ color: colors.foreground }}
            numberOfLines={1}>
            {selectedChat.name}
          </Text>
        </View>
      </View>

      <KeyboardAvoidingView
        className="flex-1"
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        <GiftedChat
          messages={messages}
          textInputStyle={{ fontFamily: 'Regular' }}
          onSend={(messages) => onSend(messages)}
          user={user}
          minInputToolbarHeight={0}
          bottomOffset={-27}
          renderBubble={renderBubble}
          renderInputToolbar={(props) => (
            <InputToolbar {...props} containerStyle={{ borderTopWidth: 0 }} />
          )}
          renderSend={customRenderSend || defaultRenderSend}
          renderComposer={customRenderComposer || renderComposer}
          renderChatEmpty={messagesLoading ? renderLoadingMessages : renderChatEmpty}
          renderMessageImage={renderMessageImage}
          renderDay={renderDay}
          renderAvatar={null}
          showUserAvatar={{ left: false, right: false }}
          keyboardShouldPersistTaps="never"
          showAvatarForEveryMessage={false}
          renderAvatarOnTop={false}
          maxComposerHeight={45}
          listViewProps={{
            showsVerticalScrollIndicator: false,
          }}
          isKeyboardInternallyHandled={true}
          alwaysShowSend
          scrollToBottom
          scrollToBottomComponent={() => (
            <View
              className="h-8 w-8 items-center justify-center rounded-full"
              style={{
                backgroundColor: colors.primary,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 3,
              }}>
              <Ionicons name="chevron-down" size={16} color="white" />
            </View>
          )}
          timeTextStyle={{
            left: { color: colors.grey, fontSize: 10 },
            right: { color: 'rgba(255, 255, 255, 0.7)', fontSize: 10 },
          }}
          textInputProps={{
            placeholder: 'Message...',
            placeholderTextColor: colors.grey,
            style: {
              color: colors.foreground,
              backgroundColor: isDark ? colors.grey5 : colors.grey6,
              borderRadius: 18,
              paddingHorizontal: 15,
              paddingVertical: 10,
              fontSize: 16,
            },
          }}
          showUserAvatar={false}
          showAvatarForEveryMessage={false}
          maxInputLength={1000}
        />
      </KeyboardAvoidingView>

      <PhotoViewModal
        visible={isModalVisible}
        photo={selectedImage}
        onClose={() => {
          setIsModalVisible(false);
          setSelectedImage(null);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  emptyChatContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    padding: 20,
  },
  emptyChatContent: {
    width: '100%',
    maxWidth: 400,
    padding: 10,
  },
  emptyChatTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 30,
  },
  suggestionCards: {
    gap: 16,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    padding: 20,
  },
  loadingContent: {
    alignItems: 'center',
    gap: 12,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ActiveChat;
