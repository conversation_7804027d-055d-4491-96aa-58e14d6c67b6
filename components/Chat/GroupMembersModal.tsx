import React from 'react';
import { View, Text, TouchableOpacity, FlatList, Image, Modal, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GroupMember } from '../../types/chat_type';
import { useColorScheme } from '../../lib/useColorScheme';

interface GroupMembersModalProps {
  isVisible: boolean;
  onClose: () => void;
  members: GroupMember[];
  groupName: string;
  currentUserId: string;
  onRemoveMember?: (memberId: string) => void;
  canManageMembers?: boolean;
  groupType?: 'COMMUNITY' | 'EVENT';
  onLeaveCommunity?: () => void;
}

const GroupMembersModal = ({
  isVisible,
  onClose,
  members,
  groupName,
  currentUserId,
  onRemoveMember,
  canManageMembers = false,
  groupType,
  onLeaveCommunity,
}: GroupMembersModalProps) => {
  const { colors } = useColorScheme();

  const renderMember = ({ item }: { item: GroupMember }) => {
    const isCurrentUser = item.id === currentUserId;
    const canRemove = canManageMembers && !isCurrentUser && !item.isAdmin;

    return (
      <View
        className="flex-row items-center border-b px-4 py-3"
        style={{ borderBottomColor: colors.grey5 }}>
        <Image
          source={{
            uri:
              (Array.isArray(item.avatar) && item.avatar.length > 0
                ? item.avatar[item.avatar.length - 1]?.secureUrl
                : typeof item.avatar === 'string'
                  ? item.avatar
                  : null) ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          }}
          className="h-12 w-12 rounded-full"
        />
        <View className="ml-3 flex-1">
          <View className="flex-row items-center">
            <Text
              className="font-medium text-base"
              style={{ color: colors.foreground }}
              numberOfLines={1}>
              {item.name}
              {isCurrentUser && ' (You)'}
            </Text>
            {item.isAdmin && (
              <View
                className="ml-2 rounded-full px-2 py-1"
                style={{ backgroundColor: colors.primary }}>
                <Text className="font-medium text-xs text-white">Admin</Text>
              </View>
            )}
          </View>
          <Text className="mt-1 text-sm" style={{ color: colors.grey }} numberOfLines={1}>
            {item.email}
          </Text>
        </View>
        {canRemove && (
          <TouchableOpacity onPress={() => onRemoveMember?.(item.id)} className="p-2">
            <Ionicons name="remove-circle-outline" size={20} color={colors.destructive} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderHeader = () => (
    <View className="px-4 py-3">
      <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
        {groupName}
      </Text>
      <Text className="mt-1 text-sm" style={{ color: colors.grey }}>
        {members.length} {members.length === 1 ? 'member' : 'members'}
      </Text>
    </View>
  );

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}>
      <SafeAreaView className="flex-1" style={{ backgroundColor: colors.background }}>
        {/* Header */}
        <View
          className="flex-row items-center justify-between border-b px-4 py-3"
          style={{ borderBottomColor: colors.grey5 }}>
          <Text className="text-xl font-semibold" style={{ color: colors.foreground }}>
            Members
          </Text>
          <TouchableOpacity onPress={onClose} className="p-1">
            <Ionicons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Members List */}
        <FlatList
          data={members}
          keyExtractor={(item) => item.id}
          renderItem={renderMember}
          ListHeaderComponent={renderHeader}
          showsVerticalScrollIndicator={false}
          className="flex-1"
          ListEmptyComponent={() => (
            <View className="flex-1 items-center justify-center py-12">
              <Ionicons name="people-outline" size={80} color={colors.grey} />
              <Text
                className="mt-4 text-center font-medium text-lg"
                style={{ color: colors.foreground }}>
                No members found
              </Text>
            </View>
          )}
        />

        {/* Leave Community Button for non-admin users */}
        {groupType === 'COMMUNITY' && !canManageMembers && onLeaveCommunity && (
          <View className="border-t px-4 py-4" style={{ borderTopColor: colors.grey5 }}>
            <TouchableOpacity
              onPress={onLeaveCommunity}
              className="flex-row items-center justify-center rounded-lg py-3"
              style={{ backgroundColor: colors.destructive }}>
              <Ionicons name="exit-outline" size={20} color="white" />
              <Text className="ml-2 font-medium text-white">Leave Community</Text>
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
};

export default GroupMembersModal;
