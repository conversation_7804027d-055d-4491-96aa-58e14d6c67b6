import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import TicketService from '~/services/TicketService';
import { TicketResponse } from '~/types/ticket';

export interface EventTicketsBottomSheetHandle {
  present: (eventId: string) => void;
  dismiss: () => void;
}

interface EventTicketsBottomSheetProps {
  onClose?: () => void;
}

const EventTicketsBottomSheet = forwardRef<
  EventTicketsBottomSheetHandle,
  EventTicketsBottomSheetProps
>(({ onClose }, ref) => {
  const { colors } = useColorScheme();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [tickets, setTickets] = useState<TicketResponse[]>([]);
  const [loading, setLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    present: (id: string) => {
      bottomSheetRef.current?.expand();
      fetchTickets(id);
    },
    dismiss: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const fetchTickets = async (id: string) => {
    setLoading(true);
    try {
      const tickets = await TicketService.getTicketsByEvent(id);
      setTickets(tickets);
    } catch (error: any) {
      console.error('Error fetching event tickets:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to load tickets',
        position: 'bottom',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    bottomSheetRef.current?.close();
    if (onClose) {
      onClose();
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'MMM dd, yyyy');
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getTicketTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'vip':
        return '#f59e0b';
      case 'premium':
        return '#3b82f6';
      case 'standard':
      default:
        return '#8b5cf6';
    }
  };

  const renderTicketItem = (ticket: TicketResponse, index: number) => {
    const ticketColor = getTicketTypeColor(ticket.ticketType);

    return (
      <View
        key={ticket.id}
        className="p-4 mb-4 border rounded-xl"
        style={{
          backgroundColor: colors.grey5,
          borderColor: colors.grey4,
        }}>
        {/* Header with ticket number and status */}
        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center">
            <View
              className="items-center justify-center w-8 h-8 mr-3 rounded-full"
              style={{ backgroundColor: `${ticketColor}20` }}>
              <MaterialIcons name="confirmation-number" size={16} color={ticketColor} />
            </View>
            {/*    <Text className="text-sm font-medium" style={{ color: colors.grey }}>
              #{ticket.ticketNumber}
            </Text> */}
          </View>
          <View className="px-2 py-1 rounded-full" style={{ backgroundColor: '#10b98120' }}>
            <Text className="text-xs font-medium text-green-600">{ticket.ticketStatus}</Text>
          </View>
        </View>

        {/* Ticket holder info */}
        <View className="flex-row items-center mb-3">
          <Image
            source={{
              uri:
                (ticket.user.profilePicture && ticket.user.profilePicture[0]?.secureUrl) ||
                'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
            }}
            className="w-10 h-10 mr-3 rounded-full"
          />
          <View className="flex-1">
            <Text className="text-base font-semibold" style={{ color: colors.foreground }}>
              {ticket.user.fullName}
            </Text>
            <Text className="text-sm" style={{ color: colors.grey }}>
              {ticket.user.email}
            </Text>
          </View>
        </View>

        {/* Ticket details */}
        <View className="flex-row items-center justify-between mb-2">
          <View className="flex-1">
            <Text className="text-sm" style={{ color: colors.grey }}>
              Ticket Type
            </Text>
            <Text className="text-base font-medium" style={{ color: colors.foreground }}>
              {ticket.ticketType}
            </Text>
          </View>
          <View className="items-end flex-1">
            <Text className="text-sm" style={{ color: colors.grey }}>
              Price
            </Text>
            <Text className="text-base font-bold" style={{ color: colors.foreground }}>
              ${ticket.price.toFixed(2)}
            </Text>
          </View>
        </View>

        {/* Purchase date and reference */}
        <View className="pt-3 border-t" style={{ borderTopColor: colors.grey4 }}>
          <View className="flex-row items-center justify-between">
            <View>
              <Text className="text-xs" style={{ color: colors.grey }}>
                Purchased on
              </Text>
              <Text className="text-sm font-medium" style={{ color: colors.foreground }}>
                {formatDate(ticket.createdAt)} at {formatTime(ticket.createdAt)}
              </Text>
            </View>
            <View className="items-end">
              <Text className="text-xs" style={{ color: colors.grey }}>
                Ref: {ticket.transactionReferenceNumber}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={['80%']}
      enablePanDownToClose
      onClose={handleClose}
      backgroundStyle={{ backgroundColor: colors.background }}
      backdropComponent={RenderBackdrop}
      handleIndicatorStyle={{ backgroundColor: colors.foreground }}>
      <BottomSheetView style={{ flex: 1 }}>
        {/* Header */}
        <View
          className="flex-row items-center justify-between px-4 py-4 border-b"
          style={{ borderBottomColor: colors.grey5 }}>
          <Text className="text-lg font-bold" style={{ color: colors.foreground }}>
            Event Tickets
          </Text>
          <TouchableOpacity onPress={handleClose} className="p-2">
            <Ionicons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView
          className="flex-1 px-4"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingTop: 16, paddingBottom: 32 }}>
          {loading ? (
            <View className="items-center justify-center py-12">
              <ActivityIndicator size="large" color={colors.primary} />
              <Text className="mt-4 text-base" style={{ color: colors.grey }}>
                Loading tickets...
              </Text>
            </View>
          ) : tickets.length === 0 ? (
            <View className="items-center justify-center py-12">
              <View
                className="items-center justify-center w-16 h-16 mb-4 rounded-full"
                style={{ backgroundColor: colors.grey5 }}>
                <MaterialIcons name="confirmation-number" size={32} color={colors.grey} />
              </View>
              <Text className="mb-2 text-lg font-semibold" style={{ color: colors.foreground }}>
                No Tickets Found
              </Text>
              <Text className="text-base text-center" style={{ color: colors.grey }}>
                No tickets have been purchased for this event yet.
              </Text>
            </View>
          ) : (
            <>
              {/* Summary */}
              <View
                className="p-4 mb-6 rounded-xl"
                style={{ backgroundColor: colors.primary + '10' }}>
                <View className="flex-row items-center justify-between">
                  <View>
                    <Text className="text-2xl font-bold" style={{ color: colors.foreground }}>
                      {tickets.length}
                    </Text>
                    <Text className="text-sm" style={{ color: colors.grey }}>
                      {tickets.length === 1 ? 'Ticket Sold' : 'Tickets Sold'}
                    </Text>
                  </View>
                  <View className="items-end">
                    <Text className="text-xl font-bold" style={{ color: colors.foreground }}>
                      ${tickets.reduce((sum, ticket) => sum + ticket.price, 0).toFixed(2)}
                    </Text>
                    <Text className="text-sm" style={{ color: colors.grey }}>
                      Total Revenue
                    </Text>
                  </View>
                </View>
              </View>

              {/* Tickets List */}
              <Text className="mb-4 text-lg font-bold" style={{ color: colors.foreground }}>
                Purchased Tickets
              </Text>

              {tickets.map((ticket, index) => renderTicketItem(ticket, index))}
            </>
          )}
        </ScrollView>
      </BottomSheetView>
    </BottomSheet>
  );
});

EventTicketsBottomSheet.displayName = 'EventTicketsBottomSheet';

export default EventTicketsBottomSheet;
