import React, { useRef, forwardRef, useImperativeHandle, useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator, Platform } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView, BottomSheetModal } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import ImagePicker from 'react-native-image-crop-picker';
import { FileService } from '~/services/FileService';
import { Toast } from 'toastify-react-native';

interface EventPhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

export interface EventPhotoUploadBottomSheetProps {
  eventId: string;
  onPhotoUploaded: (photos: EventPhoto[]) => void;
  maxPhotos?: number;
  currentPhotoCount?: number;
}

export interface EventPhotoUploadBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const EventPhotoUploadBottomSheet = forwardRef<
  EventPhotoUploadBottomSheetHandle,
  EventPhotoUploadBottomSheetProps
>(({ eventId, onPhotoUploaded, maxPhotos = 10, currentPhotoCount = 0 }, ref) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isUploading, setIsUploading] = useState(false);

  useImperativeHandle(ref, () => ({
    present: () => {
      bottomSheetModalRef.current?.expand();
    },
    dismiss: () => bottomSheetModalRef.current?.close(),
  }));

  const uploadImageFile = async (uri: string) => {
    try {
      setIsUploading(true);

      // Check if we've reached the limit
      if (currentPhotoCount >= maxPhotos) {
        Toast.show({
          type: 'warning',
          text1: 'Photo Limit Reached',
          text2: `You can only upload ${maxPhotos} photos for this event.`,
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
        return;
      }

      // Create file object from URI
      const filename = uri.split('/').pop() || 'image.jpg';
      const fileType = filename.split('.').pop() || 'jpg';

      const file = {
        uri,
        type: `image/${fileType}`,
        name: filename,
      } as any;

      const response = await FileService.uploadManyImages([file], eventId, 'EVENT_UPLOAD');

      // Update local photos state with the latest data from the response
      onPhotoUploaded(response.body.eventUploads || []);

      bottomSheetModalRef.current?.close();
      Toast.show({
        type: 'success',
        text1: 'Photo Added',
        text2: 'Your photo has been added to the event.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error: any) {
      console.error('Error uploading image:', error);
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: error.message || 'Failed to upload photo. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const takePhoto = async () => {
    try {
      const image = await ImagePicker.openCamera({
        width: 400,
        height: 400,
        cropping: true,
        compressImageQuality: 0.8,
        freeStyleCropEnabled: true,
        includeBase64: false,
      });

      await uploadImageFile(image.path);
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        Alert.alert('Permission required', 'Camera permission is required to take photos');
      }
    }
  };

  const chooseFromLibrary = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 400,
        height: 400,
        cropping: true,
        compressImageQuality: 0.8,
        includeBase64: false,
        freeStyleCropEnabled: true,
        mediaType: 'photo',
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      await uploadImageFile(image.path);
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        Alert.alert('Permission required', 'Gallery permission is required to select photos');
      }
    }
  };

  return (
    <BottomSheet
      ref={bottomSheetModalRef}
      index={-1}
      snapPoints={['60%']}
      backdropComponent={RenderBackdrop}
      backgroundStyle={{
        backgroundColor: colors.background,
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.grey,
        width: 40,
        height: 4,
      }}>
      <BottomSheetView style={{ flex: 1, padding: 20 }}>
        <View className="mb-6">
          <Text className={`font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Add Event Photo ({currentPhotoCount}/{maxPhotos})
          </Text>
          <Text className={`mt-2 text-base ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            {currentPhotoCount >= maxPhotos
              ? `You've reached your photo limit of ${maxPhotos} photos.`
              : `Choose how you'd like to add a photo to your event. ${maxPhotos - currentPhotoCount} photo${maxPhotos - currentPhotoCount === 1 ? '' : 's'} remaining.`}
          </Text>
        </View>

        <View className="flex gap-3">
          <TouchableOpacity
            onPress={takePhoto}
            disabled={isUploading || currentPhotoCount >= maxPhotos}
            className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading || currentPhotoCount >= maxPhotos ? 'opacity-50' : ''}`}
            style={{ backgroundColor: colors.grey5 }}>
            {isUploading ? (
              <ActivityIndicator
                size="small"
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            ) : (
              <Ionicons
                name="camera"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
            )}
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
              {isUploading
                ? 'Uploading...'
                : currentPhotoCount >= maxPhotos
                  ? 'Limit Reached'
                  : 'Take Photo'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={chooseFromLibrary}
            disabled={isUploading || currentPhotoCount >= maxPhotos}
            className={`h-14 flex-row items-center rounded-lg px-4 py-3 ${isUploading || currentPhotoCount >= maxPhotos ? 'opacity-50' : ''}`}
            style={{ backgroundColor: colors.grey5 }}>
            <MaterialCommunityIcons
              name="image"
              size={22}
              color={isDark ? '#fff' : '#000'}
              style={{ marginRight: 12 }}
            />
            <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
              {currentPhotoCount >= maxPhotos ? 'Limit Reached' : 'Choose from Gallery'}
            </Text>
          </TouchableOpacity>
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
});

EventPhotoUploadBottomSheet.displayName = 'EventPhotoUploadBottomSheet';

export default EventPhotoUploadBottomSheet;
