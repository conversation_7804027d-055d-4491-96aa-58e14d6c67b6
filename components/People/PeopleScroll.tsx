import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useRef, useEffect } from 'react';
import { Dimensions, Text, View, Image, Platform, Pressable } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';

import PersonProfileSheet, {
  PersonProfileSheetHandle,
} from '~/components/People/PersonProfileSheet';
import { useColorScheme } from '~/lib/useColorScheme';
import { useEvent } from '~/providers/MapProvider';

const PeopleScroll: React.FC = () => {
  const { colors } = useColorScheme();
  const { People, zoomToLocation, setUserId } = useEvent();
  const { width } = Dimensions.get('window');
  const router = useRouter();
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);

  const handleViewStory = (userId: string | number) => {
    router.push({
      pathname: '/story/view',
      params: { userId },
    });
  };

  const handlePersonPress = (person: any) => {
    setUserId(person.id);
  };

  // Zoom to the first person's location when component becomes visible and People data is available
  useEffect(() => {
    if (People && People.length > 0) {
      const firstPerson = People[0];
      // Check if the person has valid coordinates
      if (
        firstPerson.lat &&
        firstPerson.long &&
        !isNaN(firstPerson.lat) &&
        !isNaN(firstPerson.long) &&
        firstPerson.lat !== 0 &&
        firstPerson.long !== 0
      ) {
        const coordinates = {
          latitude: firstPerson.lat,
          longitude: firstPerson.long,
        };
        // Use a small delay to ensure the component is properly mounted
        setTimeout(() => {
          zoomToLocation(coordinates);
        }, 500);
      }
    }
  }, [People, zoomToLocation]);

  return (
    <View
      style={{
        zIndex: 0,
        bottom: verticalScale(Platform.OS === 'ios' ? -4 : -8),
        width: '100%',
        position: 'absolute',
        backgroundColor: 'transparent',
      }}>
      <PersonProfileSheet
        ref={personProfileSheetRef}
        onViewStory={handleViewStory}
        onChat={(userId) => {
          router.push({
            pathname: '/chat',
            params: { userId, name: People.find((p) => p.id === userId)?.name },
          });
        }}
      />
      <Carousel
        data={People}
        height={verticalScale(Platform.OS === 'ios' ? 100 : 110)}
        loop
        snapEnabled
        width={width}
        style={{
          width,
        }}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 1,
          parallaxScrollingOffset: moderateScale(60),
        }}
        onSnapToItem={(item) => {
          const coordinates = {
            latitude: People[item].lat,
            longitude: People[item].long,
          };
          zoomToLocation(coordinates);
        }}
        onProgressChange={() => {}}
        renderItem={({ item }) => {
          return (
            <Pressable onPress={() => handlePersonPress(item)}>
              <View
                style={{
                  backgroundColor: colors.background,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
                className="ml-[16px] h-28 w-[80vw] flex-row items-center rounded-lg p-4">
                {/* Avatar */}
                <View
                  style={{
                    height: moderateScale(64),
                    width: moderateScale(64),
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: scale(12),
                  }}>
                  <View
                    style={{
                      backgroundColor: colors.card,
                      height: moderateScale(60),
                      width: moderateScale(60),
                      borderRadius: moderateScale(30),
                      overflow: 'hidden',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderColor: '#5A4FCF',
                    }}>
                    {item.profilePhoto ? (
                      <Image
                        source={{ uri: item.profilePhoto }}
                        style={{ width: '100%', height: '100%' }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Ionicons name="person" size={moderateScale(30)} color={colors.primary} />
                    )}
                  </View>
                </View>

                {/* Online Indicator */}

                {/* Person Info */}
                <View style={{ flex: 1 }}>
                  <View className="flex-row items-center">
                    <Text
                      style={{ color: colors.foreground }}
                      className="font-bold text-lg"
                      numberOfLines={1}>
                      {item.name || 'Unknown Person'}
                    </Text>
                  </View>

                  {item.bio && (
                    <Text
                      style={{ color: colors.foreground, opacity: 0.8 }}
                      className="text-xs"
                      numberOfLines={1}>
                      {item.bio}
                    </Text>
                  )}

                  {item.interests && item.interests.length > 0 && (
                    <View className="mt-1 flex-row flex-wrap">
                      {item.interests.slice(0, 2).map((interest, idx) => (
                        <View
                          key={idx}
                          style={{
                            backgroundColor: colors.card,
                            paddingHorizontal: scale(6),
                            paddingVertical: verticalScale(2),
                            borderRadius: scale(10),
                            marginRight: scale(4),
                          }}>
                          <Text style={{ color: colors.primary }} className="text-xs">
                            {interest}
                          </Text>
                        </View>
                      ))}
                      {item.interests.length > 2 && (
                        <Text
                          style={{ color: colors.foreground, opacity: 0.6 }}
                          className="ml-1 self-center text-xs">
                          +{item.interests.length - 2}
                        </Text>
                      )}
                    </View>
                  )}
                </View>
              </View>
            </Pressable>
          );
        }}
      />
    </View>
  );
};

export default PeopleScroll;
